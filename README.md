
# Community Tool Lending Platform

A modern web application that enables communities to share and borrow tools efficiently. Built with [Next.js](https://nextjs.org), this platform helps reduce costs, promote sustainability, and foster collaboration among neighbors.

---

## 🚀 Features
- List and browse available tools
- Search and filter tools by category or keyword
- Add, edit, and remove tool listings
- User-friendly interface with responsive design
- MongoDB integration for persistent storage

---

## 📋 Table of Contents
- [Features](#-features)
- [Getting Started](#-getting-started)
- [Usage](#-usage)
- [Screenshots](#-screenshots)
- [Contributing](#-contributing)
- [License](#-license)
- [Contact](#-contact)

---

## 🛠️ Getting Started

### Prerequisites
- [Node.js](https://nodejs.org/) (v18 or higher recommended)
- [npm](https://www.npmjs.com/) (comes with Node.js)
- [MongoDB](https://www.mongodb.com/) (local or cloud instance)

### Installation

Clone the repository:
```bash
git clone https://github.com/your-username/community-tool-lending-platform.git
cd community-tool-lending-platform/my-tool-lending-app
```

Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
# or
bun install
```

### Environment Variables

Create a `.env.local` file in the root of `my-tool-lending-app` and add your MongoDB connection string:
```env
MONGODB_URI=your_mongodb_connection_string
```

### Running Locally

Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to view the app.

---

## 📖 Usage
- **Browse Tools:** View all available tools on the homepage.
- **Search/Filter:** Use the search bar to find specific tools.
- **Add Tool:** Click "Add Tool" to list a new item.
- **Edit/Remove:** Manage your listings from the tool details page.

---

## 🖼️ Screenshots

Below are screenshots demonstrating key features and pages of the application:

### Homepage
![Homepage Example 1](public/example-homepage1.png)
![Homepage Example 2](public/example-homepage2.png)
![Homepage Example 3](public/example-homepage3.png)
![Homepage Example](public/example-homepage.png)

### Add Tool Page
![Add Tool Page](public/add.png)

### Update Tool Page
![Update Tool Page](public/update.png)

### System Architecture
![System Architecture](public/Ssytem%20Architecture.png)

---

## 🤝 Contributing

Contributions are welcome! Please open an issue or submit a pull request for any improvements or bug fixes.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/YourFeature`)
3. Commit your changes (`git commit -m 'Add some feature'`)
4. Push to the branch (`git push origin feature/YourFeature`)
5. Open a pull request

---

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](../LICENSE) file for details.

---

## 📬 Contact

For questions or feedback, please contact:

- GitHub: [Dewick75](https://github.com/Dewick75)
- Portfolio: [pasinduwickramasinghe.great-site.net](https://pasinduwickramasinghe.great-site.net/)

---

> _Built with ❤️ using Next.js and MongoDB._
